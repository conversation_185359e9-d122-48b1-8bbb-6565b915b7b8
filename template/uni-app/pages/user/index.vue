<template>
	<view class="new-users copy-data" :style="{height:pageHeight}">
		<!-- 状态栏 -->
		<view class="status-bar" :style="{height:sysHeight}"></view>
		<!-- 用户信息卡片 - 紫色渐变背景 -->
		<view class="user-header-card">
			<view class="user-info-section">
				<view class="avatar-container">
					<!-- #ifndef APP-PLUS -->
					<view class="avatar-box" :class="{on:userInfo.is_money_level}">
						<image class="avatar" :src='userInfo.avatar' v-if="userInfo.avatar" @click="goEdit()">
						</image>
						<image v-else class="avatar" src="/static/images/f.png" mode="" @click="goEdit()">
						</image>
					</view>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="avatar-box" :class="{on:userInfo.is_money_level}">
						<image class="avatar" :src='userInfo.avatar' v-if="userInfo.avatar" @click="goEdit()">
						</image>
						<image v-else class="avatar" src="/static/images/f.png" mode="" @click="goEdit()">
						</image>
						<view class="headwear" v-if="userInfo.is_money_level && userInfo.svip_open">
							<image src="/static/images/headwear.png"></image>
						</view>
					</view>
					<!-- #endif -->
				</view>
				<view class="user-details">
					<!-- #ifdef MP || APP-PLUS -->
					<view class="user-name" v-if="!userInfo.uid" @click="openAuto">
						{{$t('请点击授权')}}
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="user-name" v-if="!userInfo.uid" @click="openAuto">
						{{$t(isWeixin ? '请点击授权' : '请点击登录')}}
					</view>
					<!-- #endif -->
					<view class="user-name" v-if="userInfo.uid">
						<text class="nickname">{{userInfo.nickname}}</text>
						<image class="vip-icon" :src="userInfo.vip_icon" v-if="userInfo.vip_icon"></image>
						<view class="svip-badge" v-if="userInfo.is_money_level> 0 && userInfo.svip_open">
							<image src="/static/images/svip.png"></image>
						</view>
					</view>
					<view class="user-id" v-if="userInfo.uid">
						用户编号：{{userInfo.uid || '1001466'}}
					</view>
					<!-- 绑定手机号 -->
					<view class="phone-section" v-if="!userInfo.phone && isLogin">
						<!-- #ifdef MP -->
						<button class="bind-phone-btn" open-type="getPhoneNumber"
							@getphonenumber="getphonenumber">{{$t('绑定手机号')}}</button>
						<!-- #endif -->
						<!-- #ifndef MP -->
						<view class="bind-phone-btn" @tap="bindPhone">
							{{$t('绑定手机号')}}
						</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
		</view>

		<view class="mid" style="flex:1;overflow: hidden;">
			<scroll-view scroll-y="true" style="height: 100%;">

				<!-- 商城订单区域 -->
				<view class="order-section">
					<view class="section-header">
						<view class="section-title">商城订单</view>
						<navigator class="view-all" hover-class="none" url="/pages/goods/order_list/index"
							open-type="navigate">
							查看订单
							<text class="iconfont icon-xiangyou"></text>
						</navigator>
					</view>
					<view class="order-icons">
						<block v-for="(item,index) in orderMenu" :key="item.title">
							<navigator class="order-item" hover-class="none" :url="item.url">
								<view class="icon-wrapper">
									<image :src="item.img" class="icon"></image>
									<text class="order-status-num" v-if="item.num > 0">{{ item.num }}</text>
								</view>
								<view class="order-text">{{$t(item.title)}}</view>
							</navigator>
						</block>
					</view>
				</view>

				<!-- 分销中心区域 -->
				<!-- TODO: 分销中心功能需要根据实际业务逻辑补充 -->
				<view class="distribution-section">
					<view class="section-header">
						<view class="section-title">分销中心</view>
						<view class="view-all">
							查看详情
							<text class="iconfont icon-xiangyou"></text>
						</view>
					</view>
					<view class="distribution-card">
						<view class="promotion-banner">
							<view class="promotion-text">
								<view class="main-text">分享好友 推广佣金</view>
								<view class="sub-text">好友成功下单后即可获得佣金</view>
							</view>
							<view class="share-btn">立即分享</view>
						</view>
						<view class="stats-row">
							<view class="stat-item">
								<view class="stat-number">20</view>
								<view class="stat-label">已推广人数</view>
							</view>
							<view class="stat-item">
								<view class="stat-number">68</view>
								<view class="stat-label">已推广总单数</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 底部菜单列表 -->
				<view class="bottom-menu-section">
					<!-- 账号与安全 -->
					<view class="menu-item" @click="goMenuPage('/pages/users/user_info/index')">
						<view class="menu-text">账号与安全</view>
						<text class="iconfont icon-xiangyou"></text>
					</view>

					<!-- 收货地址 -->
					<view class="menu-item" @click="goMenuPage('/pages/users/user_address_list/index')">
						<view class="menu-text">收货地址</view>
						<text class="iconfont icon-xiangyou"></text>
					</view>

					<!-- 我的协议 -->
					<view class="menu-item" @click="goMenuPage('/pages/users/privacyList/index')">
						<view class="menu-text">我的协议</view>
						<text class="iconfont icon-xiangyou"></text>
					</view>

					<!-- 联系客服 -->
					<!-- #ifdef MP -->
					<button class="menu-item contact-service" open-type='contact' v-if="routineContact == 1">
						<view class="menu-text">联系客服</view>
						<text class="iconfont icon-xiangyou"></text>
					</button>
					<view class="menu-item" v-else @click="goMenuPage('/pages/users/message_center/index')">
						<view class="menu-text">联系客服</view>
						<text class="iconfont icon-xiangyou"></text>
					</view>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<view class="menu-item" @click="goMenuPage('/pages/users/message_center/index')">
						<view class="menu-text">联系客服</view>
						<text class="iconfont icon-xiangyou"></text>
					</view>
					<!-- #endif -->
				</view>

				<!-- TODO: 保留原有功能但隐藏，供后续需要时启用 -->
				<!-- 原有轮播图功能 -->
				<view class="hidden-features" style="display: none;">
					<view class="slider-wrapper" v-if="imgUrls.length>0 && my_banner_status">
						<swiper indicator-dots="true" :autoplay="autoplay" :circular="circular" :interval="interval"
							:duration="duration" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff">
							<block v-for="(item,index) in imgUrls" :key="index">
								<swiper-item>
									<view @click="goPages(item.url)"
										class='slide-navigator acea-row row-between-wrapper' hover-class='none'>
										<image :src="item.pic" class="slide-image"></image>
									</view>
								</swiper-item>
							</block>
						</swiper>
					</view>

					<!-- 原有我的服务菜单 -->
					<view class="user-menus">
						<view class="menu-title">{{$t('我的服务')}}</view>
						<view class="list-box">
							<!-- #ifdef APP-PLUS || H5 -->
							<block v-for="(item,index) in MyMenus" :key="index">
								<view class="item" v-if="item.url!='#' && item.url!='/pages/service/index'"
									@click="goMenuPage(item.url, item.name)">
									<image :src="item.pic"></image>
									<text>{{$t(item.name)}}</text>
								</view>
							</block>
							<!-- #endif -->
							<!-- #ifdef MP -->
							<block v-for="(item,index) in MyMenus" :key="index">
								<view class="item" v-if="item.url!='#'
									&& item.url!='/pages/service/index'
									&& item.url!='/pages/extension/customer_list/chat'
									|| (item.url=='/pages/extension/customer_list/chat' && routineContact == 0)"
									@click="goMenuPage(item.url, item.name)">
									<image :src="item.pic"></image>
									<text>{{$t(item.name)}}</text>
								</view>
							</block>
							<!-- #endif -->
						</view>
					</view>

					<!-- 原有商家管理菜单 -->
					<view class="user-menus" v-if="storeMenu.length">
						<view class="menu-title">{{$t('商家管理')}}</view>
						<view class="list-box">
							<block v-for="(item,index) in storeMenu" :key="index">
								<view class="item" :url="item.url" hover-class="none"
									v-if="item.url!='#' && item.url!='/pages/service/index'"
									@click="goMenuPage(item.url, item.name)">
									<image :src="item.pic"></image>
									<text>{{$t(item.name)}}</text>
								</view>
							</block>
						</view>
					</view>
				</view>

				<view class="uni-p-b-98"></view>
			</scroll-view>
			<editUserModal :isShow="editModal" @closeEdit="closeEdit" @editSuccess="editSuccess">
			</editUserModal>
		</view>
		<tabBar v-if="!is_diy" :pagePath="'/pages/user/index'"></tabBar>
		<pageFooter v-else></pageFooter>
	</view>
</template>
<script>
	let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	import {
		getMenuList,
		getUserInfo,
		setVisit,
		mpBindingPhone
	} from '@/api/user.js';
	import {
		wechatAuthV2,
		silenceAuth
	} from '@/api/public.js'
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapState,
		mapGetters
	} from "vuex";
	// #ifdef H5
	import Auth from '@/libs/wechat';
	// #endif
	const app = getApp();
	import dayjs from '@/plugin/dayjs/dayjs.min.js';
	import Routine from '@/libs/routine';
	import colors from '@/mixins/color';
	import tabBar from "@/pages/index/visualization/components/tabBar.vue";
	import pageFooter from '@/components/pageFooter/index.vue'
	import {
		getCustomer
	} from '@/utils/index.js'
	import editUserModal from '@/components/eidtUserModal/index.vue'
	export default {
		components: {
			tabBar,
			pageFooter,
			editUserModal
		},
		// computed: mapGetters(['isLogin','cartNum']),
		computed: {
			...mapGetters({
				cartNum: 'cartNum',
				isLogin: 'isLogin'
			})
		},
		filters: {
			coundTime(val) {
				var setTime = val * 1000
				var nowTime = new Date()
				var rest = setTime - nowTime.getTime()
				var day = parseInt(rest / (60 * 60 * 24 * 1000))
				// var hour = parseInt(rest/(60*60*1000)%24) //小时
				return day + this.$t('day')
			},
			dateFormat: function(value) {
				return dayjs(value * 1000).format('YYYY-MM-DD');
			}
		},
		mixins: [colors],
		data() {
			return {
				editModal: false, // 编辑头像信息
				storeMenu: [], // 商家管理
				orderMenu: [{
						img: require("@/static/images/user/dzf.png"),
						title: '待支付',
						url: '/pages/goods/order_list/index?status=0'
					},
					{
						img: require("@/static/images/user/dfh.png"),
						title: '待发货',
						url: '/pages/goods/order_list/index?status=1'
					},
					{
						img: require("@/static/images/user/dsh.png"),
						title: '待收货',
						url: '/pages/goods/order_list/index?status=2'
					},
					{
						img: require("@/static/images/user/dpj.png"),
						title: '待评价',
						url: '/pages/goods/order_list/index?status=3'
					},
					{
						img: require("@/static/images/user/tksh.png"),
						title: '退款售后',
						url: '/pages/users/user_return_list/index'
					},
				],
				imgUrls: [],
				autoplay: true,
				circular: true,
				interval: 3000,
				duration: 500,
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: true, //是否隐藏授权
				orderStatusNum: {},
				userInfo: {},
				MyMenus: [],
				sysHeight: sysHeight,
				mpHeight: 0,
				showStatus: 1,
				activeRouter: '',
				// #ifdef H5 || MP
				pageHeight: '100%',
				routineContact: 0,
				// #endif
				// #ifdef APP-PLUS
				pageHeight: app.globalData.windowHeight,
				// #endif
				// #ifdef H5
				isWeixin: Auth.isWeixin(),
				//#endif
				footerSee: false,
				member_style: 1,
				my_banner_status: 1,
				is_diy: uni.getStorageSync('is_diy'),
				copyRightPic: '/static/images/support.png', //版权图片
			}
		},
		onLoad(option) {
			uni.hideTabBar()
			let that = this;
			// #ifdef MP
			// 小程序静默授权
			if (!this.$store.getters.isLogin) {
				// Routine.getCode()
				// 	.then(code => {
				// 		Routine.silenceAuth(code).then(res => {
				// 			this.onLoadFun();
				// 		})
				// 	})
				// 	.catch(res => {
				// 		uni.hideLoading();
				// 	});
			}
			// #endif

			// #ifdef H5 || APP-PLUS
			if (that.isLogin == false) {
				toLogin()
			}
			//获取用户信息回来后授权
			let cacheCode = this.$Cache.get('snsapi_userinfo_code');
			let res1 = cacheCode ? option.code != cacheCode : true;
			if (this.isWeixin && option.code && res1 && option.scope === 'snsapi_userinfo') {
				this.$Cache.set('snsapi_userinfo_code', option.code);
				Auth.auth(option.code).then(res => {
					this.getUserInfo();
				}).catch(err => {})
			}
			// #endif
			// #ifdef APP-PLUS
			that.$set(that, 'pageHeight', app.globalData.windowHeight);
			// #endif

			let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
			let curRoute = routes[routes.length - 1].route //获取当前页面路由
			this.activeRouter = '/' + curRoute
			this.getCopyRight();
		},
		onReady() {
			let self = this
			// #ifdef MP
			let info = uni.createSelectorQuery().select(".sys-head");
			info.boundingClientRect(function(data) { //data - 各种参数
				self.mpHeight = data.height
			}).exec()
			// #endif
		},
		onShow: function() {
			let that = this;
			// #ifdef APP-PLUS
			uni.getSystemInfo({
				success: function(res) {
					that.pageHeight = res.windowHeight + 'px'
				}
			});
			// #endif
			if (that.isLogin) {
				this.getUserInfo();
				this.getMyMenus();
				this.setVisit();
			};
			this.getCopyRight();
		},
		onPullDownRefresh() {
			this.onLoadFun();
		},
		methods: {
			getWechatuserinfo() {
				//#ifdef H5
				Auth.isWeixin() && Auth.toAuth('snsapi_userinfo', '/pages/user/index');
				//#endif
			},
			editSuccess() {
				this.editModal = false
				this.getUserInfo();
			},
			closeEdit() {
				this.editModal = false
			},
			// 记录会员访问
			setVisit() {
				setVisit({
					url: '/pages/user/index'
				}).then(res => {})
			},
			// 打开授权
			openAuto() {
				toLogin();
			},
			// 授权回调
			onLoadFun() {
				this.getUserInfo();
				this.getMyMenus();
				this.setVisit();
			},
			Setting: function() {
				uni.openSetting({
					success: function(res) {}
				});
			},
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e
			},
			// 绑定手机
			bindPhone() {
				uni.navigateTo({
					url: '/pages/users/user_phone/index'
				})
			},
			getphonenumber(e) {
				if (e.detail.errMsg == 'getPhoneNumber:ok') {
					Routine.getCode()
						.then(code => {
							let data = {
								code,
								iv: e.detail.iv,
								encryptedData: e.detail.encryptedData,
							}
							mpBindingPhone(data).then(res => {
								this.getUserInfo()
								this.$util.Tips({
									title: res.msg,
									icon: 'success'
								});
							}).catch(err => {
								return this.$util.Tips({
									title: err
								});
							})
						})
						.catch(error => {
							uni.hideLoading();
						});
				}
			},
			/**
			 * 获取个人用户信息
			 */
			getUserInfo: function() {
				let that = this;
				getUserInfo().then(res => {
					that.userInfo = res.data
					that.$store.commit("SETUID", res.data.uid);
					that.orderMenu.forEach((item, index) => {
						switch (item.title) {
							case '待付款':
								item.num = res.data.orderStatusNum.unpaid_count
								break
							case '待发货':
								item.num = res.data.orderStatusNum.unshipped_count
								break
							case '待收货':
								item.num = res.data.orderStatusNum.received_count
								break
							case '待评价':
								item.num = res.data.orderStatusNum.evaluated_count
								break
							case '售后/退款':
								item.num = res.data.orderStatusNum.refunding_count
								break
						}
					})
					uni.stopPullDownRefresh();
				});
			},
			//小程序授权api替换 getUserInfo
			getUserProfile() {
				toLogin();
			},
			/**
			 * 
			 * 获取个人中心图标
			 */
			switchTab(order) {
				this.orderMenu.forEach((item, index) => {
					switch (item.title) {
						case '待付款':
							item.img = order.dfk
							break
						case '待发货':
							item.img = order.dfh
							break
						case '待收货':
							item.img = order.dsh
							break
						case '待评价':
							item.img = order.dpj
							break
						case '售后/退款':
							item.img = order.sh
							break
					}
				})
			},
			getMyMenus: function() {
				let that = this;
				// if (this.MyMenus.length) return;
				getMenuList().then(res => {
					let storeMenu = []
					let myMenu = []
					res.data.routine_my_menus.forEach((el, index, arr) => {
						if (el.url == '/pages/admin/order/index' || el.url ==
							'/pages/admin/order_cancellation/index' || el.name ==
							'客服接待') {
							storeMenu.push(el)
						} else {
							myMenu.push(el)
						}
					})
					this.member_style = Number(res.data.diy_data.value)
					this.my_banner_status = res.data.diy_data.my_banner_status
					let order01 = {
						dfk: 'icon-daifukuan',
						dfh: 'icon-daifahuo',
						dsh: 'icon-daishouhuo',
						dpj: 'icon-daipingjia',
						sh: 'icon-a-shouhoutuikuan'
					}
					let order02 = {
						dfk: 'icon-daifukuan-lan',
						dfh: 'icon-daifahuo-lan',
						dsh: 'icon-daishouhuo-lan',
						dpj: 'icon-daipingjia-lan',
						sh: 'icon-shouhou-tuikuan-lan'
					}
					let order03 = {
						dfk: 'icon-daifukuan-ju',
						dfh: 'icon-daifahuo-ju',
						dsh: 'icon-daishouhuo-ju',
						dpj: 'icon-daipingjia-ju',
						sh: 'icon-shouhou-tuikuan-ju'
					}
					let order04 = {
						dfk: 'icon-daifukuan-fen',
						dfh: 'icon-daifahuo-fen',
						dsh: 'icon-daishouhuo-fen',
						dpj: 'icon-daipingjia-fen',
						sh: 'icon-a-shouhoutuikuan-fen'
					}
					let order05 = {
						dfk: 'icon-daifukuan-lv',
						dfh: 'icon-daifahuo-lv',
						dsh: 'icon-daishouhuo-lv',
						dpj: 'icon-daipingjia-lv',
						sh: 'icon-shouhou-tuikuan-lv'
					}
					// switch (res.data.diy_data.order_status) {
					// 	case 1:
					// 		this.switchTab(order01)
					// 		break
					// 	case 2:
					// 		this.switchTab(order02)
					// 		break
					// 	case 3:
					// 		this.switchTab(order03)
					// 		break
					// 	case 4:
					// 		this.switchTab(order04)
					// 		break
					// 	case 5:
					// 		this.switchTab(order05)
					// 		break
					// }
					that.$set(that, 'MyMenus', myMenu);
					that.$set(that, 'storeMenu', storeMenu);
					this.imgUrls = res.data.routine_my_banner
					this.routineContact = Number(res.data.routine_contact_type)
				});
			},
			// 编辑页面
			goEdit() {
				if (this.isLogin == false) {
					toLogin();
				} else {
					// #ifdef MP
					if (this.userInfo.is_default_avatar) {
						this.editModal = true
						return
					}
					// #endif
					uni.navigateTo({
						url: '/pages/users/user_info/index'
					})
				}

			},
			// 签到
			goSignIn() {
				uni.navigateTo({
					url: '/pages/users/user_sgin/index'
				})
			},

			goPages(url) {
				this.$util.JumpPath(url);
			},

			// goMenuPage
			goMenuPage(url, name) {
				if (this.isLogin) {
					if (url.indexOf('http') === -1) {
						// #ifdef H5 || APP-PLUS
						if (name && name === '客服接待') {
							// return window.location.href = `${location.origin}${url}`
							return uni.navigateTo({
								url: `/pages/annex/web_view/index?url=${location.origin}${url}`
							});
						} else if (name && name === '联系客服') {
							return getCustomer(url)

						} else if (name === '订单核销') {
							return uni.navigateTo({
								url: url
							});
							// return window.location.href = `${location.origin}${url}`
						}
						// #endif

						// #ifdef MP
						if (name && name === '联系客服') {
							return getCustomer(url)
						}
						if (url != '#' && url == '/pages/users/user_info/index') {
							uni.openSetting({
								success: function(res) {}
							});
						}
						// #endif
						uni.navigateTo({
							url: url,
							fail(err) {
								uni.switchTab({
									url: url
								})
							}
						})
					} else {
						uni.navigateTo({
							url: `/pages/annex/web_view/index?url=${url}`
						});
					}
				} else {
					// #ifdef MP
					this.openAuto()
					// #endif
				}
			},
			goRouter(item) {
				var pages = getCurrentPages();
				var page = (pages[pages.length - 1]).$page.fullPath;
				if (item.link == page) return
				uni.switchTab({
					url: item.link,
					fail(err) {
						uni.redirectTo({
							url: item.link
						})
					}
				})
			},
			getCopyRight() {
				const copyRight = uni.getStorageSync('copyRight')
				if (copyRight.copyrightImage) {
					this.copyRightPic = copyRight.copyrightImage
				}
			}
		}
	}
</script>

<style lang="scss">
	page,
	body {
		height: 100%;
	}

	.height {
		margin-top: -100rpx !important;
	}

	.unBg {
		background-color: unset !important;

		.user-info {
			.info {
				.name {
					color: #333333 !important;
					font-weight: 600;
				}

				.num {
					color: #333 !important;

					.num-txt {
						height: 38rpx;
						background-color: rgba(51, 51, 51, 0.13);
						padding: 0 12rpx;
						border-radius: 16rpx;
					}
				}
			}
		}

		.num-wrapper {
			color: #333 !important;
			font-weight: 600;

			.num-item {
				.txt {
					color: rgba(51, 51, 51, 0.7) !important;
				}
			}
		}

		.message {
			.iconfont {
				color: #333 !important;
			}

			.num {
				color: #fff !important;
				background-color: var(--view-theme) !important;
			}
		}

		.setting {
			.iconfont {
				color: #333 !important;
			}
		}
	}

	.cardVipB {
		background-color: #343A48;
		width: 100%;
		height: 124rpx;
		border-radius: 16rpx 16rpx 0 0;
		padding: 22rpx 30rpx 0 30rpx;
		margin-top: 16px;

		.left-box {
			.small {
				color: #F8D5A8;
				font-size: 28rpx;
				margin-left: 18rpx;
			}

			.pictrue {
				width: 40rpx;
				height: 45rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}

		.btn {
			color: #BBBBBB;
			font-size: 26rpx;
		}

		.icon-xiangyou {
			margin-top: 6rpx;
		}
	}

	.cardVipA {
		position: absolute;
		background: url('~@/static/images/member.png') no-repeat;
		background-size: 100% 100%;
		width: 750rpx;
		height: 84rpx;
		bottom: -2rpx;
		left: 0;
		padding: 0 56rpx 0 135rpx;

		.left-box {
			font-size: 26rpx;
			color: #905100;
			font-weight: 400;
		}

		.btn {
			color: #905100;
			font-weight: 400;
			font-size: 24rpx;
		}

		.iconfont {
			font-size: 20rpx;
			margin: 4rpx 0 0 4rpx;
		}
	}

	.new-users {
		display: flex;
		flex-direction: column;
		height: 100%;

		.sys-head {
			position: relative;
			width: 100%;
			// background: linear-gradient(90deg, $bg-star1 0%, $bg-end1 100%);

			.bg {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background: var(--view-theme);
				background-size: 100% auto;
				background-position: left bottom;
			}

			.sys-title {
				z-index: 10;
				position: relative;
				height: 43px;
				text-align: center;
				line-height: 43px;
				font-size: 36rpx;
				color: #FFFFFF;
			}
		}

		.head {
			// background: #fff;

			.user-card {
				position: relative;
				width: 100%;
				height: 380rpx;
				margin: 0 auto;
				padding: 35rpx 28rpx;
				background-image: url("~@/static/images/user01.png");
				background-size: 100% auto;
				background-color: var(--view-theme);

				.user-info {
					z-index: 20;
					position: relative;
					display: flex;

					.headwear {
						position: absolute;
						right: -4rpx;
						top: -14rpx;
						width: 44rpx;
						height: 44rpx;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.live {
						width: 28rpx;
						height: 28rpx;
						margin-left: 20rpx;
					}

					.bntImg {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						text-align: center;
						line-height: 120rpx;
						background-color: unset;
						position: relative;

						.avatarName {
							font-size: 16rpx;
							color: #fff;
							text-align: center;
							background-color: rgba(0, 0, 0, 0.6);
							height: 37rpx;
							line-height: 37rpx;
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
						}
					}

					.avatar-box {
						position: relative;
						display: flex;
						align-items: center;
						justify-content: center;
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;

						&.on {
							.avatar {
								border: 2px solid #FFAC65;
								border-radius: 50%;
								box-sizing: border-box;
							}
						}
					}

					.avatar {
						position: relative;
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;

					}

					.info {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-left: 20rpx;
						padding: 20rpx 0;

						.name {
							display: flex;
							align-items: center;
							color: #fff;
							font-size: 31rpx;

							.nickname {
								max-width: 8em;
							}

							.vip {
								margin-left: 10rpx;

								image {
									width: 78rpx;
									height: 30rpx;
									display: block;
								}
							}
						}

						.num {
							display: flex;
							align-items: center;
							font-size: 26rpx;
							color: rgba(255, 255, 255, 0.6);

							image {
								width: 22rpx;
								height: 23rpx;
								margin-left: 20rpx;
							}
						}
					}
				}

				.message {
					align-self: flex-start;
					position: relative;
					margin-top: 15rpx;
					margin-right: 20rpx;

					.num {
						position: absolute;
						top: -8rpx;
						left: 18rpx;
						padding: 0 6rpx;
						height: 28rpx;
						border-radius: 12rpx;
						background-color: #fff;
						font-size: 18rpx;
						line-height: 28rpx;
						text-align: center;
						color: var(--view-theme);
					}

					.iconfont {
						font-size: 40rpx;
						color: #fff;
					}
				}

				.num-wrapper {
					z-index: 30;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 22rpx;
					// padding: 0 47rpx;
					color: #fff;

					.num-item {
						width: 33.33%;
						text-align: center;

						&~.num-item {
							position: relative;

							&:before {
								content: '';
								position: absolute;
								width: 1rpx;
								height: 28rpx;
								top: 50%;
								margin-top: -14rpx;
								background-color: rgba(255, 255, 255, 0.4);
								left: 0;
							}
						}

						.num {
							font-size: 42rpx;
							font-weight: bold;
						}

						.txt {
							margin-top: 8rpx;
							font-size: 26rpx;
							color: rgba(255, 255, 255, 0.6);
						}
					}
				}

				.sign {
					z-index: 200;
					position: absolute;
					right: -12rpx;
					top: 80rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 120rpx;
					height: 60rpx;
					background: linear-gradient(90deg, rgba(255, 225, 87, 1) 0%, rgba(238, 193, 15, 1) 100%);
					border-radius: 29rpx 4rpx 4rpx 29rpx;
					color: #282828;
					font-size: 28rpx;
					font-weight: bold;
				}
			}

			.order-wrapper {
				background: #fff;
				margin: 0 30rpx;
				border-radius: 16rpx;
				position: relative;
				margin-top: -10rpx;

				.order-hd {
					justify-content: space-between;
					padding: 30rpx 20rpx 10rpx 30rpx;
					margin-top: 25rpx;
					font-size: 30rpx;
					color: #282828;

					.left {
						font-weight: bold;
					}

					.right {
						display: flex;
						align-items: center;
						color: #666666;
						font-size: 26rpx;

						.icon-xiangyou {
							margin-left: 5rpx;
							font-size: 26rpx;
						}
					}
				}

				.order-bd {
					display: flex;
					padding: 0 0;

					.order-item {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						width: 20%;
						height: 140rpx;

						.pic {
							position: relative;
							text-align: center;

							.iconfont {
								font-size: 48rpx;
								color: var(--view-theme);
							}

							image {
								width: 58rpx;
								height: 48rpx;
							}
						}

						.txt {
							margin-top: 6rpx;
							font-size: 26rpx;
							color: #333;
						}
					}
				}
			}
		}

		.slider-wrapper {
			margin: 20rpx 30rpx;
			height: 130rpx;

			swiper,
			swiper-item {
				height: 100%;
			}

			image {
				width: 100%;
				height: 130rpx;
				border-radius: 16rpx;
			}
		}

		.user-menus {
			background-color: #fff;
			margin: 0 30rpx;
			border-radius: 16rpx;

			.menu-title {
				padding: 30rpx 30rpx 40rpx;
				font-size: 30rpx;
				color: #282828;
				font-weight: bold;
			}

			.list-box {
				display: flex;
				flex-wrap: wrap;
				padding: 0;
			}

			.item {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;
				width: 25%;
				margin-bottom: 47rpx;
				font-size: 26rpx;
				color: #333333;

				image {
					width: 52rpx;
					height: 52rpx;
					margin-bottom: 18rpx;
				}


				&:last-child::before {
					display: none;
				}
			}

			button {
				font-size: 28rpx;
			}
		}

		.phone {
			color: #fff;
			background-color: #ffffff80;
			border-radius: 15px;
			width: max-content;
			font-size: 24rpx;
			padding: 2px 10px;
			margin-top: 8rpx;
		}

		.order-status-num {
			min-width: 20rpx;
			min-height: 20rpx;
			background-color: #fff;
			color: var(--view-theme);
			border-radius: 15px;
			position: absolute;
			right: 0rpx;
			top: 0rpx;
			font-size: 20rpx;
			padding: 0 8rpx;
			border: 1px solid var(--view-theme);
		}

		.support {
			width: 219rpx;
			height: 74rpx;
			margin: 54rpx auto;
			display: block;
		}
	}

	.card-vip {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		width: 690rpx;
		height: 134rpx;
		margin: -72rpx auto 0;
		background: url('~@/static/images/user_vip.png');
		background-size: cover;
		padding-left: 118rpx;
		padding-right: 34rpx;

		.left-box {
			font-size: 24rpx;
			color: #AE5A2A;

			.big {
				font-size: 28rpx;
			}

			.small {
				opacity: 0.8;
				margin-top: 10rpx;
			}
		}

		.btn {
			height: 52rpx;
			line-height: 52rpx;
			padding: 0 10rpx;
			text-align: center;
			background: #fff;
			border-radius: 28rpx;
			font-size: 26rpx;
			color: #AE5A2A;
		}

	}

	.setting {
		margin-top: 15rpx;
		margin-left: 15rpx;
		color: #fff;

		.iconfont {
			font-size: 40rpx;
		}
	}

	.new-users {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		// 状态栏样式
		.status-bar {
			background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
		}

		// 用户信息卡片 - 紫色渐变背景
		.user-header-card {
			background: #fff;
			padding: 24rpx 32rpx;
			margin-bottom: 24rpx;

			.user-info-section {
				background-image: url("/static/images/user/user_bg.png");
				background-size: 100% 100%;
				padding: 0 32rpx;
				height: 288rpx;
				display: flex;
				align-items: center;

				.avatar-container {
					margin-right: 30rpx;

					.avatar-box {
						position: relative;

						.avatar {
							width: 96rpx;
							height: 96rpx;
							border-radius: 50%;
							// border: 4rpx solid rgba(255, 255, 255, 0.3);
						}

						.headwear {
							position: absolute;
							top: -10rpx;
							left: -10rpx;
							right: -10rpx;
							bottom: -10rpx;
							z-index: 2;

							image {
								width: 100%;
								height: 100%;
							}
						}

						&.on {
							.avatar {
								border: 4rpx solid #FFD700;
							}
						}
					}
				}

				.user-details {
					flex: 1;
					color: #fff;

					.user-name {
						color: #ffffff;
						font-weight: 500;
						font-size: 32rpx;
						line-height: 44rpx;
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;

						.nickname {
							font-weight: 500;
							font-size: 32rpx;
							line-height: 44rpx;
							max-width: 300rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.vip-icon {
							width: 60rpx;
							height: 30rpx;
							margin-right: 10rpx;
						}

						.svip-badge {
							image {
								width: 60rpx;
								height: 30rpx;
							}
						}
					}

					.user-id {
						color: #ffffff;
						font-weight: 400;
						font-size: 24rpx;
						line-height: 34rpx;
						margin-top: 8rpx;
					}

					.phone-section {
						.bind-phone-btn {
							background: rgba(255, 255, 255, 0.2);
							border: 2rpx solid rgba(255, 255, 255, 0.3);
							border-radius: 40rpx;
							padding: 10rpx 20rpx;
							font-size: 24rpx;
							color: #fff;
							display: inline-block;
						}
					}
				}
			}
		}

		// 商城订单区域
		.order-section {
			background: #fff;
			border-radius: 16rpx;
			margin: 0 32rpx;
			padding: 24rpx 0rpx;

			.section-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 24rpx;
				margin-bottom: 48rpx;

				.section-title {
					color: #000000;
					font-weight: 500;
					font-size: 28rpx;
					line-height: 40rpx;
				}

				.view-all {
					color: #999999;
					font-weight: 400;
					font-size: 24rpx;
					line-height: 34rpx;
					display: flex;
					align-items: center;

					.iconfont {
						margin-left: 0rpx;
						font-size: 20rpx;
					}
				}
			}

			.order-icons {
				display: flex;
				justify-content: space-between;

				.order-item {
					flex: 1;
					text-align: center;

					.icon-wrapper {
						position: relative;
						display: flex;
						justify-content: center;
						align-items: center;

						.icon {
							width: 72rpx;
							height: 72rpx;
						}

						.order-status-num {
							position: absolute;
							top: 0;
							right: 22%;
							background: #ff4444;
							color: #fff;
							font-size: 20rpx;
							padding: 4rpx 8rpx;
							border-radius: 20rpx;
							min-width: 30rpx;
							min-height: 30rpx;
							text-align: center;
							line-height: 1;
						}
					}

					.order-text {
						color: #000000;
						font-weight: 400;
						font-size: 24rpx;
						line-height: 34rpx;
						margin-top: 16rpx;
					}
				}
			}
		}

		// 分销中心区域
		.distribution-section {
			background: #fff;
			border-radius: 20rpx;
			margin: 24rpx 32rpx;
			padding: 32rpx 24rpx;

			.section-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 24rpx;

				.section-title {
					color: #000000;
					font-weight: 500;
					font-size: 28rpx;
					line-height: 40rpx;
				}
				
				.view-all {
					color: #999999;
					font-weight: 400;
					font-size: 24rpx;
					line-height: 34rpx;
					display: flex;
					align-items: center;
				
					.iconfont {
						margin-left: 0rpx;
						font-size: 20rpx;
					}
				}
			}

			.distribution-card {
				
				.promotion-banner {
					background-image: url("/static/images/user/fx1.png");
					background-size: 100% 100%;
					border-radius: 16rpx;
					padding: 32rpx 23rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 16rpx;
					min-height: 128rpx;

					.promotion-text {
						.main-text {
							color: #c56f01;
							font-weight: 500;
							font-size: 28rpx;
							line-height: 40rpx;
							margin-bottom: 8rpx;
						}

						.sub-text {
							color: #c56f01;
							font-weight: 400;
							font-size: 24rpx;
							line-height: 34rpx;
						}
					}

					.share-btn {
						width: 176rpx;
						height: 56rpx;
						border-radius: 32rpx;
						background: #ffffff;
						text-align: center;
						color: #c56f01;
						font-weight: 500;
						font-size: 24rpx;
						line-height: 56rpx;
					}
				}

				.stats-row {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.stat-item {
						// text-align: center;
						padding: 32rpx 22rpx;
						background-image: url("/static/images/user/fx2.png");
						background-size: 100% 100%;
						min-width: 311rpx;
						min-height: 128rpx;

						.stat-number {
							color: #c56f01;
							font-weight: 900;
							font-size: 32rpx;
							line-height: 42rpx;
						}

						.stat-label {
							color: #c56f01;
							font-weight: 500;
							font-size: 24rpx;
							line-height: 34rpx;
						}
					}
					
					.stat-item:nth-child(2){
						background-image: url("/static/images/user/fx3.png");
					}
				}
			}
		}

		// 底部菜单区域
		.bottom-menu-section {
			background: #fff;
			border-radius: 16rpx;
			margin: 0 32rpx;
			margin-bottom: 24rpx;
			padding: 0;

			.menu-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx;
				border-bottom: 1rpx solid #f5f5f5;
				background: transparent;
				border: none;
				width: 100%;
				height: 104rpx;

				&:last-child {
					border-bottom: none;
				}

				&.contact-service {
					// 微信小程序联系客服按钮特殊样式
					font-size: inherit;
					line-height: inherit;
				}

				.menu-text {
					color: #000000;
					font-weight: 400;
					font-size: 28rpx;
					line-height: 40rpx;
				}

				.iconfont {
					font-size: 24rpx;
					color: #c8c8c8;
				}
			}
		}

		// 隐藏的原有功能
		.hidden-features {
			display: none !important;
		}
	}
</style>