<template>
	<view class='productSort copy-data' :style="{height:pageHeight}">
		<!-- #ifdef APP-PLUS || MP -->
		<!-- <view class="sys-head" :style="{height:sysHeight}"></view> -->
		<!-- #endif -->

		<!-- 搜索框 -->
		<view class='header acea-row row-center-wrapper' :class="showBtn ? 'active' : ''">
			<view class='acea-row row-between-wrapper input'>
				<text class='iconfont icon-sousuo'></text>
				<input type='text' :placeholder="$t('搜索商品')" @confirm="searchSubmitValue" confirm-type='search'
					name="search" placeholder-class='placeholder' @focus="changeShowBtn(true)"></input>
			</view>
			<view class="sbtn" @click="searchSubmitValue">搜索</view>
			<view class="mc" @click="changeShowBtn(false)"></view>
		</view>

		<!-- 分类选项卡 -->
		<view class="category-tabs">
			<scroll-view scroll-x="true" show-scrollbar="false" class="tabs-scroll">
				<view class="tabs-container">
					<view class='tab-item' :class='index==navActive?"active":""' v-for="(item,index) in productList"
						:key="index" @click='switchCategory(index)'>
						<view class='tab-picture'>
							<image :src="item.pic || defimg" mode="aspectFit"></image>
						</view>
						<text class="tab-text">{{$t(item.cate_name)}}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 标签列表 -->
		<view class='content'>
			<scroll-view scroll-x="true" style="height: 100%;" class="content-scroll">
				<view class='category-section' v-if="currentCategory">
					<!-- 全部 -->
					<view class='product-name' :class='-1==navChildActive?"active":""'
						@click='switchNavChildActive(-1)'>{{$t(`全部`)}}
						<view class="blink" v-if="-1==navChildActive"></view>
					</view>

					<!-- 子分类标签 -->
					<view v-for="(item,index) in currentCategory.children" :key="index" class='product-name'
						:class='index==navChildActive?"active":""' @click='switchNavChildActive(index)'>
						{{$t(item.cate_name)}}
						<view class="blink" v-if="index==navChildActive"></view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 排序选项 -->
		<view class="goods_menus_box">
			<view class="tabs_box">
				<view class="tabs_txt" @click="changeTabsSwitchTypeNum(0)" :class='0==tabsSwitchTypeNum?"active":""'>综合
				</view>
				<view class="tabs_txt" @click="changeTabsSwitchTypeNum(1)" :class='1==tabsSwitchTypeNum?"active":""'>新品
				</view>
				<view class="tabs_txt" @click="changeTabsSwitchTypeNum(tabsSwitchTypeNum === 2 ? 3 : 2)"
					:class='[2,3].includes(tabsSwitchTypeNum)?"active":""'>销量
					<image src="/static/images/tabs_up.png" class="icon" v-if="tabsSwitchTypeNum === 2"></image>
					<image src="/static/images/tabs_down.png" class="icon" v-else-if="tabsSwitchTypeNum === 3"></image>
					<image src="/static/images/tabs_define.png" class="icon" v-else></image>
				</view>
				<view class="tabs_txt" @click="changeTabsSwitchTypeNum(tabsSwitchTypeNum === 4 ? 5 : 4)"
					:class='[4,5].includes(tabsSwitchTypeNum)?"active":""'>价格
					<image src="/static/images/tabs_up.png" class="icon" v-if="tabsSwitchTypeNum === 4"></image>
					<image src="/static/images/tabs_down.png" class="icon" v-else-if="tabsSwitchTypeNum === 5"></image>
					<image src="/static/images/tabs_define.png" class="icon" v-else></image>
				</view>
			</view>
			<image src="/static/images/cardIcon.png" class="icon" v-if="showType === 'Link'" @click="showType = 'Card'"></image>
			<image src="/static/images/linkIcon.png" class="icon" v-if="showType === 'Card'" @click="showType = 'Link'"></image>
		</view>
		
		<!-- 商品列表 -->
		<scroll-view scroll-y="true" class="shopList-scroll" @scrolltolower="loadMore" v-if="shopList.length">
			<view class="shopBox">
				<view :class="showType === 'Link' ? 'shopLink' : 'shopCard'" v-for="(item,index) in shopList" :key="index" @click="godDetail(item)">
					<image :src="item.image" mode="aspectFit" class="img"></image>
					<view class="infoBox">
						<view class="title">{{item.store_name}}</view>
						<view class="salesTxt">已售{{item.sales}}{{item.unit_name}}</view>
						<view class="priceTxt"><span style="font-size: 28rpx;">￥</span>{{item.price}}</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<emptyBox v-else emptyText="暂无搜索的商品，换个关键词试试?" style="margin-top: 80rpx;"/>

		<tabBar v-if="!is_diy" :pagePath="'/pages/goods_cate/goods_cate'"></tabBar>
		<pageFooter v-else></pageFooter>
	</view>
</template>

<script>
	let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	import {
		getCategoryList,
		getProductslist
	} from '@/api/store.js';
	import {
		mapState
	} from "vuex"
	import pageFooter from '@/components/pageFooter/index.vue'
	import emptyBox from '@/components/emptyBox/index.vue'
	import tabBar from "@/pages/index/visualization/components/tabBar.vue";
	const app = getApp();

	export default {
		components: {
			pageFooter,
			emptyBox,
			tabBar
		},
		data() {
			return {
				defimg: require('@/static/images/tabs_all.png'),
				productList: [],
				navActive: 0,
				navChildActive: -1,
				is_diy: uni.getStorageSync('is_diy'),
				newData: {},
				activeRouter: '',
				pageHeight: '100%',
				sysHeight: sysHeight,
				// #ifdef APP-PLUS
				pageHeight: app.globalData.windowHeight,
				// #endif
				tabsSwitchTypeNum: 0,
				nowPage: 1,
				searchKey: "",
				shopList: [],
				showType: "Link",// Link or Card 
				loading: false, // 新增：加载状态
				finished: false ,// 新增：是否已加载全部
				showBtn: false,
			}
		},
		computed: {
			...mapState({
				cartNum: state => state.indexData.cartNum
			}),
			currentCategory() {
				return this.productList[this.navActive] || null;
			}
		},
		async mounted() {
			let that = this
			// #ifdef H5
			uni.getSystemInfo({
				success: function(res) {
					that.pageHeight = res.windowHeight + 'px'
				}
			});
			// #endif

			let routes = getCurrentPages();
			let curRoute = routes[routes.length - 1].route
			this.activeRouter = '/' + curRoute;

			uni.$on('uploadCatData', () => {
					this.getAllCategory(1);
				})

			!that.productList.length && await this.getAllCategory(1);
			this.getShopList();
		},
		methods: {
			getAllCategory: async function(type) {
				if (type || !uni.getStorageSync('CAT1_DATA')) {
					const res = await getCategoryList()
					// console.log(res.data)
					this.productList = res.data;
					this.productList.unshift({
						"id": 0,
						"pid": 0,
						"cate_name": "全部",
						"pic": "",
						"big_pic": "",
						"children": []
					})
					uni.setStorageSync('CAT1_DATA', this.productList)
				} else {
					this.productList = uni.getStorageSync('CAT1_DATA')
				}
			},
			switchCategory: function(index) {
				this.navActive = index;
				this.navChildActive = -1;
				this.reloadShopList();
			},
			switchNavChildActive: function(index) {
				this.navChildActive = index;
				this.reloadShopList();
			},
			searchSubmitValue: function(e) {
				this.searchKey = e.detail.value;
				this.showBtn = false;
				this.reloadShopList();
			},
			//修改筛选标签
			changeTabsSwitchTypeNum: function(index) {
				this.tabsSwitchTypeNum = index;
			},
			//获取请求参数
			getSearchObj: function() {
				let obj = {
					sid: this.navChildActive === -1 ? 0 : this.currentCategory.children[this.navChildActive].id,
					keyword: this.searchKey,
					priceOrder: [4, 5].includes(this.tabsSwitchTypeNum) ? this.tabsSwitchTypeNum === 4 ? 'asc' :
						'desc' : '',
					salesOrder: [2, 3].includes(this.tabsSwitchTypeNum) ? this.tabsSwitchTypeNum === 2 ? 'asc' :
						'desc' : '',
					news: this.tabsSwitchTypeNum === 1 ? 1 : 0,
					page: this.nowPage,
					limit: 20,
					cid: this.navChildActive === -1 ? this.currentCategory.id : 0,
					coupon_category_id: '',
					productId: ''
				}
				return obj
			},
			// 获取商品列表
			getShopList: async function() {
				if (this.loading || this.finished) return; // 避免重复加载
				this.loading = true; // 设置加载状态
				const reqData = this.getSearchObj();
				const res = await getProductslist(reqData);
				if (res.data.length === 0) {
					this.finished = true; // 没有更多数据了
				} else {
					this.shopList = [...this.shopList, ...res.data];
				}
				this.loading = false; // 结束加载状态
			},
			// 重新获取
			reloadShopList: function(){
				this.nowPage = 1;
				this.shopList = [];
				this.finished = false; // 重置加载完成状态
				this.getShopList();
			},
			// 滚动到底部加载更多
			loadMore: function() {
				if (this.loading || this.finished) return;
				this.nowPage++;
				this.getShopList();
			},
			// 是否显示搜索
			changeShowBtn(type){
				this.showBtn = type
				// console.log(type);
			},
			// 去详情页
			godDetail(item) {
				uni.navigateTo({
					url: `/pages/goods_details/index?id=${item.id}`
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.productSort {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		//#ifdef MP
		height: calc(100vh - var(--window-top)) !important;
		//#endif
		//#ifndef MP
		height: 100vh;
		//#endif
	}

	/* 搜索框样式 */
	.header {
		width: 100%;
		height: 128rpx;
		display: flex;
		place-content: center;
		place-items: center;
		padding: 0rpx 32rpx;
		background: #ffffff;
		flex-shrink: 0;
	}

	.header .input {
		width: 686rpx;
		display: flex;
		place-items: center;
		gap: 8rpx;
		padding: 16rpx 24rpx;
		border-radius: 16rpx;
		background: #f4f4f4;
		flex: 1;
		transition: 0.5s;
	}

	.header .input .iconfont {
		font-size: 35rpx;
		color: #555;
		margin-right: 16rpx;
	}

	.header .input .placeholder {
		color: #999;
	}

	.header .input input {
		font-size: 26rpx;
		flex: 1;
		height: 100%;
	}
	
	.header.active{
		.sbtn{
			display: unset;
		}
		.mc{
			display: unset;
		}
	}
	
	.sbtn{
		color: #292EC0;
		font-size: 28rpx;
		margin-left: 40rpx;
		display: none;
		cursor: pointer;
	}
	
	.mc{
		height: calc(100vh - 128rpx);
		width: 100%;
		background: #141414;
		opacity: 0.7;
		position: fixed;
		top: 128rpx;
		left: 0;
		z-index: 100;
		display: none;
	}

	/* 分类选项卡样式 */
	.category-tabs {
		background: #ffffff;
		padding: 20rpx 0;
		flex-shrink: 0;
	}

	.tabs-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.tabs-container {
		display: inline-flex;
		padding: 0 32rpx;
	}

	.tab-item {
		display: inline-flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx 24rpx 24rpx 24rpx;
		border-radius: 16rpx;
		border: 2rpx solid #f4f4f4;
		background: #ffffff;
		min-width: 144rpx;
		transition: all 0.3s ease;
		margin-right: 16rpx;
	}

	.tab-item.active {
		border-color: #292ec0;
		color: #292ec0;
	}

	.tab-picture {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 8rpx;
	}

	.tab-picture image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.tab-text {
		font-size: 24rpx;
		line-height: 34rpx;
		text-align: center;
		white-space: nowrap;
	}

	/* 内容区域样式 */
	.content {
		height: 108rpx;
		background: #ffffff;
		padding: 0 32rpx;
		width: 100%;
	}

	.content-scroll {
		width: 100%;
		height: 100%;

	}

	.category-section {
		height: 100%;
		border-bottom: 2px solid #f4f4f4;
		display: flex;
		align-items: center;
	}

	.product-name {
		font-size: 24rpx;
		color: #333;
		text-align: center;
		line-height: 108rpx;
		margin-right: 48rpx;
		height: 100%;
		flex-shrink: 0;
		// padding: 0 10rpx;
		position: relative;
	}

	.product-name .blink {
		width: 48rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background: #292ec0;
		position: absolute;
		left: calc(50% - 24rpx);
		bottom: 0;
	}

	.product-name.active {
		border-color: #292ec0;
		color: #141414;
		font-weight: bold;
		font-size: 32rpx;
	}

	.goods_menus_box {
		height: 112rpx;
		padding: 0 32rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.tabs_box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #333333;
			font-size: 28rpx;

			.tabs_txt {
				margin-right: 64rpx;
				display: flex;
				align-items: center;
				cursor: pointer;
				transition: 0.2s;
				font-weight: 400;


				.icon {
					width: 32rpx;
					height: 32rpx;
					margin-left: 4rpx;
				}
			}

			.tabs_txt.active {
				color: #292EC0;
				font-weight: 500;

			}
		}


		.icon {
			width: 48rpx;
			height: 48rpx;
		}
	}
	
	.shopList-scroll{
		flex: 1;
		.shopBox{
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			
			.shopLink{
				padding: 16rpx 32rpx;
				background-color: #fff;
				display: flex;
				align-items: center;
				width: 100%;
				
				.img{
					width: 180rpx;
					height: 180rpx;
					flex-shrink: 0;
				}
				
				.infoBox{
					flex: 1;
					margin-left: 36rpx;
				}
			}
			
			.shopCard{
				padding: 24rpx;
				background-color: #fff;
				width: 336rpx;
				border-radius: 16rpx;
				margin-top: 16rpx;
				
				.img{
					width: 260rpx;
					height: 260rpx;
				}
				
				.salesTxt{
					margin-top: 8rpx;
				}
				.priceTxt{
					margin-top: 20rpx;
				}
			}
			
			.shopCard:nth-child(odd){
				margin-right: 7rpx;
			}
			
			.shopCard:nth-child(even){
				margin-left: 7rpx;
			}
			
			.title{
				width:100%;
				max-height:80rpx;//高度不能乱写，你要自己去看你的两行文字占多少高度
				font-size:28rpx;
				line-height: 40rpx;
				color: #141414;
				font-weight: 500;
				overflow:hidden;//一定要写
				text-overflow: ellipsis;//超出省略号
				display:-webkit-box;//一定要写
				-webkit-line-clamp: 2;//控制行数
				-webkit-box-orient: vertical;//一定要写
			}
			
			.salesTxt{
				color:#999;
				font-size: 24rpx;
				line-height: 34rpx;
				font-weight: 500;
				margin-top: 16rpx;
			}
			
			.priceTxt{
				color:#FB3807;
				font-size: 36rpx;
				line-height: 42rpx;
				font-weight: 800;
				margin-top: 40rpx;
			}
			
			
		}
	}
</style>