<template>
	<view v-show="!isSortType">
		<view class="explosion" :style="'margin-top:' + mbConfig*2 +'rpx;background-color:' + boxColor+';'"
			v-if="explosiveMoney.length">
			<view class="left-box skeleton-rect">
				<view class="txt">{{$t(desConfig)}}</view>
				<view class="title" :style="'color:'+themeColor+';'">{{$t(titleConfig)}}</view>
				<view @click="goDetail(item)" v-for="(item,index) in explosiveMoney" :key="index">
					<template v-if="index === 0">
						<image :src="item.img" alt="" v-if="item.img" class="img" />
						<view class="leftbutton">{{ item.info[0].value }}
							<image src="/static/images/w-home-more.png" class="smIcon" />
						</view>
					</template>
				</view>
			</view>
			<view class="right-box skeleton-rect">
				<template v-for="(item,index) in explosiveMoney">
					<view class="item" @click="goDetail(item)"  v-if="index > 0" :key="index">
						<view class="title">{{ item.info[0].value }}
							<image src="/static/images/b-home-more.png" class="smIcon" />
						</view>
						<view class="des">{{ item.info[1].value }}</view>
						<image :src="item.img" class="img" alt="" v-if="item.img" />
					</view>
				</template>
			</view>
			<!-- <view class="hd ">
				
				
			</view>
			<view class="bd">
				<view class="item skeleton-rect" @click="goDetail(item)" v-for="(item,index) in explosiveMoney"
					:key="index">
					<view class="con-box">
						<view class="title line1">{{$t(item.info[0].value)}}</view>
						<view class="con line2">{{$t(item.info[1].value)}}</view>
						<view class="go">GO！<image src="/static/images/right-icon.png" mode=""></image>
						</view>
					</view>
					<image :src="item.img" mode="aspectFill"></image>
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	export default {
		name: 'activeParty',
		props: {
			dataConfig: {
				type: Object,
				default: () => {}
			},
			isSortType: {
				type: String | Number,
				default: 0
			}
		},
		data() {
			return {
				titleConfig: this.dataConfig.titleConfig.value,
				desConfig: this.dataConfig.desConfig.value,
				explosiveMoney: this.dataConfig.menuConfig.list,
				themeColor: this.dataConfig.themeColor.color[0].item,
				bgColor: this.dataConfig.bgColor.color,
				mbConfig: this.dataConfig.mbConfig.val,
				boxColor: this.dataConfig.boxColor.color[0].item
			};
		},
		created() {},
		methods: {
			goDetail(item) {
				let urls = item.info[2].value
				this.$util.JumpPath(urls);
			}
		}
	}
</script>

<style lang="scss">
	.explosion {
		width: 710rpx;
		margin-top: 20rpx;
		margin: 20rpx auto 0 auto;
		padding: 36rpx 28rpx;
		background-image: url('/static/images/bgCard.png');
		background-size: 100% 100%;
		border-radius: 16px;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.smIcon {
			width: 34rpx;
			height: 34rpx;
			margin-left: 8rpx;
		}

		.left-box {
			.txt {
				color: #999999;
				font-weight: 400;
				font-size: 24rpx;
				line-height: 34rpx;
			}

			.title {
				height: 50rpx;
				color: #13155a;
				font-weight: 600;
				font-size: 36rpx;
				line-height: 50rpx;
				margin-top: 10rpx;
				width: 4em;
				padding-bottom: 60rpx;
				background-image: url("/static/images/txtImg.png");
				background-size: 128rpx 28rpx;
				background-repeat: no-repeat;
				background-position: right bottom;
			}

			.img {
				height: 160rpx;
				width: 280rpx;
			}

			.leftbutton {
				width: 280rpx;
				height: 72rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				opacity: 0.8;
				border-radius: 36rpx;
				background: linear-gradient(244.73deg, rgba(84, 88, 218, 1) 14.58%, rgba(41, 46, 192, 1) 134.75%);
				color: rgba(255, 255, 255, 1);
				font-weight: bold;
				font-size: 28rpx;
				line-height: 40rpx;
				margin-top: -20rpx;
			}
			
			
		}
		
		.right-box{
			
			
			.item {
			    padding: 24rpx;
			    padding-bottom: 32rpx;
			    width: 320rpx;
			    background-color: #fff;
			    border-radius: 16rpx;
			    box-shadow: 2rpx 8rpx 8rpx rgba(84, 88, 218, 0.1);
			    position: relative;
			    margin-top: 24rpx;
			
			    .title {
			        color: #13155a;
			        font-weight: 600;
			        font-size: 28rpx;
			        line-height: 40rpx;
			        display: flex;
			        align-items: center;
			    }
			
			    .des {
			        color: #999999;
			        font-weight: 400;
			        font-size: 24rpx;
			        line-height: 34rpx;
			        margin-top: 16rpx;
			    }
			
			    .img {
			        position: absolute;
			        top: -10rpx;
			        right: 24rpx;
					width: 104rpx;
					height: 72rpx;
			    }
			}
			
			.item:nth-child(2) {
			    .img {
			        top: 20rpx;
					height: 108rpx;
			    }
			}
		}
	}
</style>