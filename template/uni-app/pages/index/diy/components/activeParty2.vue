<template>
	<view v-show="!isSortType">
		<view class="explosion" :style="'margin-top:' + mbConfig*2 +'rpx;'" v-if="explosiveMoney.length">
			<view class="hd skeleton-rect">
				<!-- <image src="/static/images/explosion-title.png" mode=""></image> -->
				<view class="title" :style="'color:'+themeColor+';'">{{$t(titleConfig)}}</view>
			</view>
			<view class="bd">
				<view class="item skeleton-rect" @click="goDetail(item)" v-for="(item,index) in explosiveMoney"
					:key="index">
					<image :src="item.img" mode="aspectFill" class="img"></image>
					<view class="title line1">{{$t(item.info[0].value)}}
						<image src="/static/images/b-home-more.png" mode="aspectFill" class="xicon"></image>
					</view>
					<view class="con line2">{{$t(item.info[1].value)}}</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'activeParty2',
		props: {
			dataConfig: {
				type: Object,
				default: () => {}
			},
			isSortType: {
				type: String | Number,
				default: 0
			}
		},
		data() {
			return {
				titleConfig: this.dataConfig.titleConfig.value,
				explosiveMoney: this.dataConfig.menuConfig.list,
				themeColor: this.dataConfig.themeColor.color[0].item,
				bgColor: this.dataConfig.bgColor.color,
				mbConfig: this.dataConfig.mbConfig.val,
				boxColor: this.dataConfig.boxColor.color[0].item
			};
		},
		created() {},
		methods: {
			goDetail(item) {
				let urls = item.info[2].value
				this.$util.JumpPath(urls);
			}
		}
	}
</script>

<style lang="scss">
	.explosion {
		width: 710rpx;
		margin-top: 20rpx;
		margin: 20rpx auto 0 auto;
		padding: 32rpx 24rpx;
		background-size: 100% 100%;
		border-radius: 32rpx;
		box-sizing: border-box;
		background-image: url('/static/images/bgCard2.png');

		.hd {
			display: flex;
			align-items: center;

			.title {
				font-size: 32rpx;
				font-weight: bold;
				margin-right: 12rpx;
			}

			// image {
			// 	width: 147rpx;
			// 	height: 35rpx;
			// 	margin-right: 20rpx;
			// }

			.txt {
				padding: 0 10rpx;
				height: 36rpx;
				line-height: 36rpx;
				// background: linear-gradient(90deg, rgba(255, 168, 0, 1) 0%, rgba(255, 34, 15, 1) 100%);
				border-radius: 26rpx 0px 26rpx 0px;
				color: #fff;
				text-align: center;
				font-size: 22rpx;
				box-shadow: 3px 1px 1px 1px var(--view-minorColorT);
			}
		}

		.bd {
			display: flex;
			flex-wrap: wrap;
			margin-top: 16rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.item {
				position: relative;
				width: 206rpx;
				padding: 16rpx 14rpx;
				background-color: #fff;
				border-radius: 16rpx;
				box-sizing: border-box;

				.img {
					width: 96rpx;
					height: 96rpx;
					margin-left: calc(50% - 48rpx);
				}

				.title {
					display: flex;
					align-items: center;
					justify-content: center;
					color: #13155a;
					font-weight: 600;
					font-size: 28rpx;

					.xicon {
						width: 34rpx;
						height: 34rpx;
						margin-left: 8rpx;
					}
				}

				.con {
					font-size: 20rpx;
					line-height: 28rpx;
					padding: 4rpx 12rpx;
					border-radius: 22rpx;
					background: rgba(255, 236, 58, 1);
					color: rgba(197, 111, 1, 1);
					text-align: center;
					margin-top: 8rpx;
				}

				&:first-child .go {
					background: linear-gradient(90deg, rgba(75, 196, 255, 1) 0%, rgba(32, 126, 255, 1) 100%);
				}

				&:nth-child(2) .go {
					background: linear-gradient(90deg, rgba(255, 144, 67, 1) 0%, rgba(255, 83, 29, 1) 100%);
				}

				&:nth-child(3) .go {
					background: linear-gradient(90deg, rgba(150, 225, 135, 1) 0%, rgba(72, 206, 44, 1) 100%);
				}

				&:nth-child(4) .go {
					background: linear-gradient(90deg, rgba(255, 197, 96, 1) 0%, rgba(255, 156, 0, 1) 100%);
				}

				&:nth-child(2n) {
					margin-right: 0;
				}
			}
		}
	}
</style>