<template>
	<view>
		<view :class="{ mask: invShow }" @touchmove.stop.prevent @click="invClose"></view>
		<view class="popup" :class="{ on: invShow }">
			<view class="popup-hd">{{$t(`抬头选择`)}}<text class="iconfont icon-guanbi" @click="invClose"></text></view>
			<scroll-view class="popup-bd" scroll-y="true">
				<radio-group v-if="invList.length" name="inv" @change="invChange">
					<template v-for="(item, index) in invList">
						<label v-if="item.type === 1 || item.type === 2 && isSpecial" :key="item.id"
							class="acea-row row-middle item">
							<radio class="radio" :value="item.id" :checked="item.id === invChecked" />
							<view class="text">
								<view class="acea-row row-middle">
									<view class="name-wrap acea-row row-middle">
										<view class="name-group">
											<view class="name">{{item.name}}</view>
											<view v-if="item.is_default" class="default">{{$t(`默认`)}}</view>
										</view>
									</view>
									<view class="type" :class="{special: item.type === 2}">
										{{item.header_type === 1 ? $t(`个人`) : $t(`企业`)}}
										{{item.type === 1 ? $t(`普通`) : $t(`专用`)}}
									</view>
								</view>
								<view class="acea-row row-bottom">
									<view class="info-wrap">
										<view class="email">{{$t(`联系邮箱`)}} {{item.email}}</view>
										<view v-if="item.header_type === 1" class="tel">{{$t(`联系电话`)}}
											{{item.drawer_phone}}
										</view>
										<view v-else class="number">{{$t(`企业税号`)}}{{item.duty_number}}</view>
									</view>
									<navigator v-if="!isOrder" class="navigator"
										:url="`/pages/users/user_invoice_form/index?from=order_confirm&id=${item.id}&${urlQuery}`"
										hover-class="none"><text class="iconfont icon-bianji"></text>{{$t(`编辑`)}}
									</navigator>
									<navigator v-else class="navigator"
										:url="`/pages/users/user_invoice_form/index?from=order_details&id=${item.id}&order_id=${orderId}`"
										hover-class="none"><text class="iconfont icon-bianji"></text>{{$t(`编辑`)}}
									</navigator>
								</view>
							</view>
						</label>
					</template>
				</radio-group>
				<view v-else class="empty">
					<image :src="imgHost + '/statics/images/noInvoice.png'"></image>
					<view>{{$t(`您还没有添加发票信息哟`)}}~</view>
				</view>
			</scroll-view>
			<view class="popup-ft">
				<navigator v-if="!isOrder" class="navigator"
					:url="`/pages/users/user_invoice_form/index?from=order_confirm&${urlQuery}`" hover-class="none">
					<text class="iconfont icon-fapiao"></text>{{$t(`添加新的抬头`)}}
				</navigator>
				<navigator v-else class="navigator"
					:url="`/pages/users/user_invoice_form/index?order_id=${orderId}&from=order_details&${urlQuery}`"
					hover-class="none">
					<text class="iconfont icon-fapiao"></text>{{$t(`添加新的抬头`)}}
				</navigator>
				<button class="button" plain @click="invCancel">{{$t(`不开发票`)}}</button>
				<button class="button" plain @click="invSub">{{$t(`确认提交`)}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		HTTP_REQUEST_URL
	} from '@/config/app';
	export default {
		data() {
			return {
				imgHost: HTTP_REQUEST_URL,
				invId: 0
			}
		},
		props: {
			invShow: {
				type: Boolean,
				default: false
			},
			invList: {
				type: Array,
				default () {
					return [];
				}
			},
			invChecked: {
				type: String,
				default: ''
			},
			isSpecial: {
				type: Boolean,
				default: false
			},
			urlQuery: {
				type: String,
				default: ''
			},
			isOrder: {
				type: Number,
				default: 0
			},
			orderId: {
				type: String,
				default: ''
			}
		},
		methods: {
			invClose(state) {
				this.$emit('inv-close');
			},
			invChange(e) {
				if (this.isOrder) {
					this.invId = e.detail.value
				} else {
					this.$emit('inv-change', e.detail.value);
				}
			},
			invSub() {
				this.$emit('inv-change', this.invId || this.invChecked);
			},
			invCancel() {
				this.$emit('inv-cancel');
			}
		},
	}
</script>

<style lang="scss" scoped>
	/deep/uni-radio .uni-radio-input {
		margin-right: 0;
	}

	.popup {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 9;
		width: 100%;
		border-top-left-radius: 16rpx;
		border-top-right-radius: 16rpx;
		background-color: #F5F5F5;
		transform: translateY(100%);
		transition: 0.3s;
	}

	.popup.on {
		transform: translateY(0);
	}

	.popup-hd {
		position: relative;
		height: 129rpx;
		font-size: 32rpx;
		line-height: 129rpx;
		text-align: center;
		color: #000000;

		.iconfont {
			position: absolute;
			top: 50%;
			right: 30rpx;
			transform: translateY(-50%);
			font-size: 32rpx;
			color: #707070;
		}
	}

	.popup-bd {
		height: 600rpx;
		padding-right: 30rpx;
		padding-left: 30rpx;
		box-sizing: border-box;

		.item {
			height: 194rpx;
			padding: 30rpx;
			margin-bottom: 14rpx;
			box-sizing: border-box;
			background: url("data:image/png;base64,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") center/cover no-repeat;
		}

		.text {
			flex: 1;
			min-width: 0;
			margin-left: 30rpx;
		}

		.name-wrap {
			flex: 1;
			min-width: 0;
		}

		.name-group {
			display: flex;
			align-items: center;
			max-width: 100%;
		}

		.name {
			flex: 1;
			min-width: 0;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			font-weight: bold;
			font-size: 30rpx;
			color: #282828;
			line-height: 28rpx;
		}

		.default {
			max-width: 100rpx;
			border: 1rpx solid var(--view-theme);
			margin-left: 20rpx;
			border-radius: 6rpx;
			padding: 0 4rpx;
			font-size: 20rpx;
			line-height: 28rpx;
			text-align: center;
			color: var(--view-theme);
		}

		.email {
			margin-top: 16rpx;
			font-size: 24rpx;
			color: #666666;
		}

		.tel {
			margin-top: 12rpx;
			font-size: 24rpx;
			color: #666666;
		}

		.number {
			margin-top: 12rpx;
			font-size: 24rpx;
			color: #666666;
		}

		.info-wrap {
			flex: 1;
		}

		.type {
			width: 162rpx;
			height: 42rpx;
			margin-left: 20rpx;
			background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUQAAABUCAYAAAD6S07pAAAEtUlEQVR4Xu3d31FTQRQH4F0D76QCSQVqBWIHWoGWoBWIHdiBWoF2oFYgVpB0EN4B17kIMxFD7rLmIZz98prd4Z7v7Pxm7x9u8vnpoiQfAgQIEEhZIFoFBAgQ+CMgEK0EAgQIXAkIREuBAAECAtEaIECAwN8CdohWBAECBOwQrQECBAjYIVoDBAgQWCvglNnCIECAgFNma4AAAQJOma0BAgQIOGW2BggQILBJwDVE64MAAQKuIVoDBAgQcA3RGiBAgIBriNYAAQIEXEO0BggQIFAh4KZKBZIhBAj0ISAQ++izKgkQqBAQiBVIhhAg0IeAQOyjz6okQKBCQCBWIBlCgEAfAgKxjz6rkgCBCgGBWIFkCAECfQgIxD76rEoCBCoEBGIFkiEECPQhIBD76LMqCRCoEBCIFUiGECDQh0A+W85Pcs6P+ihXlQQIEFgvUEr5mYevynJ+eJHSccr5JSwCBAh0JVDKp0lKx3k6W1wG4vXnbDk/Sim9t2PsajkolkCXAsOOMKX0en86+3YN8FcgXu0WD85T+pJzftqlkqIJEAgvUEr5vpfS8zydna4W+08gruwWXVsMvywUSKA/gWFnuD+dPV5X+a2BWJbzg4uUTlLOD/sjUzEBAhEFhjDcS+no5s7w1lPmm9cUc85fI8KoiQCB/gRKKc9WrxneFLh1h3g98Hw5/+juc38LR8UEwgmU8mlvOnu1qa7RQLx8JCfneTgcBREg0JXApJTZ8GjNfwXiMNnD212tG8USCCew6UbKarGjO8SrQDzOOb8Np6QgAgS6ECilvNufzo7Hiq0NxCM3V8YofU+AwK4KjN1MuT7uqkB0HXFX2+y4CBCoEZiUMr3tUZs7nzIPE85PF6XmDxtDgACBXRPYOzis2vxVDRKIu9Zex0OAwF0EthqITpnvQm8sAQK7JrDVU+bhLThuquxaix0PAQK1Alu9qXK2nHvsplbeOAIEdk5gu4/dnC5+5JTWvh1i5yp3QAQIELghUFI62T84fDIGM3pTxfXDMULfEyBwHwS28q97Xu5wH1rtGAkQGBX435c7uJkySmwAAQL3SGDs5srmF8Tm/COldHiP6nWoBAgQ2CSwmJTy5M4viD1zI8WyIkAgoMCmGyxrf2TqIufPKaXhF/h8CBAgEFHg26SUFxt/ZOrqmuEHp8kR+68mAgRWBYadYirlzT8/Q3r5aM2DB29TKRtfr42TAAEC4QRy/jj59evd5Q/Vu1YYrr0KIkCgQWDYMWav9WqQM4UAgZACAjFkWxVFgECLgEBsUTOHAIGQAgIxZFsVRYBAi4BAbFEzhwCBkAICMWRbFUWAQIuAQGxRM4cAgZACAjFkWxVFgECLgEBsUTOHAIGQAgIxZFsVRYBAi4BAbFEzhwCBkAICMWRbFUWAQIuAQGxRM4cAgZACAjFkWxVFgECLgEBsUTOHAIGQAgIxZFsVRYBAi4BAbFEzhwCBkAICMWRbFUWAQIuAQGxRM4cAgZACAjFkWxVFgECLgEBsUTOHAIGQAgIxZFsVRYBAi4BAbFEzhwCBkAICMWRbFUWAQIuAQGxRM4cAgZACAjFkWxVFgECLgEBsUTOHAIGQAgIxZFsVRYBAi8BvRCCv/9ohX8AAAAAASUVORK5CYII=") center/cover no-repeat;
			font-size: 24rpx;
			line-height: 42rpx;
			text-align: center;
			color: #D67300;

			&.special {
				background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUQAAABUCAYAAAD6S07pAAAEkElEQVR4Xu3dQY4TRxQG4Kq25Bl2zCKyzCZwAuAETG6QnCA5QnKCwA1yA5gTwA2AE+A5AaywrCzIEiJ5KvLYVmYYt7sQXkz3+7x1jdTvq19PXVU97bxczEvyIUCAAIGUNUQpIECAwFpAQ5QEAgQIbAQ0RFEgQICAhigDBAgQuC7gDlEiCBAg4A5RBggQIOAOUQYIECCwU8CSWTAIECBgySwDBAgQsGSWAQIECFgyywABAgT2CdhDlA8CBAjYQ5QBAgQI2EOUAQIECNhDlAECBAjYQ5QBAgQIVAg4VKlAMoQAgRgCGmKMeVYlAQIVAhpiBZIhBAjEENAQY8yzKgkQqBDQECuQDCFAIIaAhhhjnlVJgECFgIZYgWQIAQIxBDTEGPOsSgIEKgQ0xAokQwgQiCGgIcaYZ1USIFAhoCFWIBlCgEAMgbxcfJyllB/GKFeVBAgQaBMo53n1Vfk0v3/xb3qaUvoVFgECBIIJnDXj9DSfTD9cNsTtp/w9P724KH+5YwwWB+USCClQzpsm/55/mL7Zln+tIa7vFj/dXX75/Crn9CSkkaIJEBi8QCnp7ejo+Od8cvLP1WJvNMTtl/YWB58JBRIIKlDOR5N7j3YV39oQV3eKF18+z1JOPwZVUzYBAoMTKOfN+M7p13eGrUvmm3uK6fXgTBREgEBIgaZJP13dM/waofUO8f+l8/yF0+eQ2VE0gaEJnI0m09/2FdXZEDeP5Lwfmox6CBCIJdCM04PVozXf1RBXf+yAJVZwVEtgeALtBylXa+28Q1w3xPnqoe0/h4ekIgIEggg8G02mqz6291PVENcPbDtc6cL0PQECt1Og6zBle9V1DXH9r332EW/nXLsqAgQ6BJrx8UnbozbfvGTeLJsLdQIECPRRYDSZVt38VQ3SEPsYAddMgMBW4KAN0aM3gkWAQJ8FDrpkdqjS5yi4dgIEDnqo4rEbgSJAoOcCh3vsZrn4+C6lvPPtED1HcvkECIQQKLPR5N7jrlI7D1XsH3YR+p4AgT4IHORf95YLL3fow2S7RgIEOgW+7+UODlM6gQ0gQKBHAl2HK10viH2Xcrrfo3pdKgECBNoFSvrQHB0//uYXxDpIkSoCBIYp0H7A0vYjUy9zTqfDxFAVAQLRBUpJb0ZHx7/s/ZGpyz3DZXpumRw9LuonEEGgzJom/3HjZ0g3j9as3ne49/XaEYjUSIBAOIEXzTg9u/yhenuF4SZfwQQI7BQos7xczL3WSzwIECCQUtIQxYAAAQIbAQ1RFAgQIKAhygABAgSuC7hDlAgCBAi4Q5QBAgQIuEOUAQIECOwUsGQWDAIECFgyywABAgQsmWWAAAEClswyQIAAgX0C9hDlgwABAvYQZYAAAQL2EGWAAAEC9hBlgAABAvYQZYAAAQIVAg5VKpAMIUAghoCGGGOeVUmAQIWAhliBZAgBAjEENMQY86xKAgQqBDTECiRDCBCIIaAhxphnVRIgUCGgIVYgGUKAQAwBDTHGPKuSAIEKAQ2xAskQAgRiCGiIMeZZlQQIVAhoiBVIhhAgEENAQ4wxz6okQKBC4D/gApk7R56F9gAAAABJRU5ErkJggg==");
				color: #E93323;
			}
		}

		.navigator {
			margin-left: 20rpx;
			font-size: 26rpx;
			color: #282828;

			.iconfont {
				margin-right: 10rpx;
				font-size: 26rpx;
				color: #000000;
			}
		}
	}

	.popup-ft {
		padding: 14rpx 30rpx 44rpx;

		.navigator {
			height: 86rpx;
			border-radius: 43rpx;
			background-color: var(--view-theme);
			font-size: 30rpx;
			line-height: 86rpx;
			text-align: center;
			color: #FFFFFF;

			.iconfont {
				margin-right: 14rpx;
				font-size: 30rpx;
			}
		}

		.button {
			height: 86rpx;
			border: 1rpx solid var(--view-theme);
			border-radius: 43rpx;
			margin-top: 26rpx;
			font-size: 30rpx;
			line-height: 84rpx;
			color: var(--view-theme);
		}
	}

	.empty {
		padding-top: 58rpx;
		font-size: 26rpx;
		text-align: center;
		color: #999999;

		.image {
			width: 400rpx;
			height: 260rpx;
			margin-bottom: 20rpx;
		}
	}
</style>
