"use strict";function t(t){for(var e=Object.create(null),n=t.attributes.length;n--;)e[t.attributes[n].name]=t.attributes[n].value;return e}function e(){o[1]&&(this.src=o[1],this.onerror=null),this.onclick=null,this.ontouchstart=null,uni.postMessage({data:{action:"onError",source:"img",attrs:t(this)}})}function n(a,s,i){for(var c=0;c<a.length;c++){var u;!function(c){var l=a[c],d=void 0;if(l.type&&"node"!=l.type)d=document.createTextNode(l.text.replace(/&amp;/g,"&"));else{var g=l.name;"svg"==g&&(i="http://www.w3.org/2000/svg"),"html"!=g&&"body"!=g||(g="div"),d=i?document.createElementNS(i,g):document.createElement(g);for(var p in l.attrs)d.setAttribute(p,l.attrs[p]);if(l.children&&n(l.children,d,i),"img"==g)!d.src&&d.getAttribute("data-src")&&(d.src=d.getAttribute("data-src")),l.attrs.ignore||(d.onclick=function(e){e.stopPropagation(),uni.postMessage({data:{action:"onImgTap",attrs:t(this)}})}),o[2]&&(u=new Image,u.src=d.src,d.src=o[2],u.onload=function(){d.src=this.src},u.onerror=function(){d.onerror()}),d.onerror=e;else if("a"==g)d.addEventListener("click",function(e){e.stopPropagation(),e.preventDefault();var n,o=this.getAttribute("href");o&&"#"==o[0]&&(n=(document.getElementById(o.substr(1))||{}).offsetTop),uni.postMessage({data:{action:"onLinkTap",attrs:t(this),offset:n}})},!0);else if("video"==g||"audio"==g)r.push(d),l.attrs.autoplay||(l.attrs.controls||d.setAttribute("controls","true"),l.attrs.poster||d.setAttribute("poster","data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'/>")),o[3]&&(d.onplay=function(){for(var t=0;t<r.length;t++)r[t]!=this&&r[t].pause()}),d.onerror=function(){uni.postMessage({data:{action:"onError",source:g,attrs:t(this)}})};else if("table"==g&&o[4]&&!d.style.cssText.includes("inline")){var h=document.createElement("div");h.style.overflow="auto",h.appendChild(d),d=h}else"svg"==g&&(i=void 0)}s.appendChild(d)}(c)}}document.addEventListener("UniAppJSBridgeReady",function(){document.body.onclick=function(){return uni.postMessage({data:{action:"onClick"}})},uni.postMessage({data:{action:"onJSBridgeReady"}})});var o,r=[];window.setContent=function(t,e,a){var s=document.getElementById("content");e[0]&&(document.body.bgColor=e[0]),e[5]||(s.style.userSelect="none"),a||(s.innerHTML="",r=[]),o=e;var i=document.createDocumentFragment();n(t,i),s.appendChild(i);var c=s.scrollHeight;uni.postMessage({data:{action:"onLoad",height:c}}),clearInterval(window.timer);var u=!1;window.timer=setInterval(function(){s.scrollHeight!=c?(c=s.scrollHeight,uni.postMessage({data:{action:"onHeightChange",height:c}})):u||(u=!0,uni.postMessage({data:{action:"onReady"}}))},350)},window.onunload=function(){clearInterval(window.timer)};