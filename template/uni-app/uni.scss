/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* crmeb颜色变量 */
$theme-color:#E93323; // 主题色-主色
$theme-color-opacity:rgba(233,51,35,.6); // 主题色-透明色
$bg-star: #f62c2c;  // 主题渐变色-开始
$bg-end:#f96e29; // 主题渐变色-结束
$kf-theme : #3875EA; // 客服主题色
$kf-star:#3875EA; // 客服渐变色
$kf-end:#1890FC; // 客服渐变色
$bg-star1: #F73730;  // 主题渐变色1-开始
$bg-end1:#F86429;   // 主题渐变色1-结束



/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-page-bg-color:#f5f5f5;
// $uni-page-bg-color:#fff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24upx;
$uni-font-size-base:28upx;
$uni-font-size-lg:32upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;
$uni-border-radius-index: 12rpx; // 首页模块radius
/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;
$uni-index-margin-col: 30rpx; // 模块水平magin
/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;
$uni-index-margin-row: 20rpx; // 模块垂直magin
/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度
$uni-index-box-shadow: 0rpx 3rpx 10rpx 2rpx rgba(0, 0, 0, 0.03);
/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;
$uni-color-tip: #ccc; // 底部提示文字颜色

// 首页字号
$uni-index-title-font-size: 34rpx;