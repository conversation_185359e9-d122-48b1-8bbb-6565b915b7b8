<template>
	<view class="emptyBox">
		<image src="/static/images/emptyImg.png" class="img"></image>
		<view class="text">{{ emptyText }}</view>
	</view>
</template>

<script>
	export default {
		name: 'd_emptyBox',
		props: {
			emptyText: {
				type: String,
				default: () => "暂无内容"
			},
		},
		data: function() {
			return {};
		},
	}
</script>

<style lang="scss">
	.emptyBox {
		width: 100%;
		padding: 40rpx 0;

		.img {
			width: 240rpx;
			height: 240rpx;
			margin-left: calc(50% - 120rpx);
		}

		.text {
			text-align: center;
			color: #808080;
			font-weight: 400;
			font-size: 28rpx;
			line-height: 40rpx;
			margin-top: 40rpx;
		}

	}
</style>